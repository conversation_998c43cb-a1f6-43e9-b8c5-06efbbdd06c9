/**
 * 插件管理器组件
 */
import React, { useState, useEffect } from 'react';
import {
  Modal,
  List,
  Card,
  Switch,
  Button,
  Space,
  Tag,
  Avatar,
  Descriptions,
  Tabs,
  Input,
  Select,
  Upload,
  message,
  Popconfirm,
  Tooltip,
  Badge
} from 'antd';
import {
  AppstoreOutlined,
  SettingOutlined,
  DeleteOutlined,
  UploadOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PanelPluginManager, { PanelPlugin } from '../../services/PanelPluginManager';
import './PluginManager.less';

// const { TabPane } = Tabs; // 已弃用，使用 items 属性
const { Search } = Input;

interface PluginManagerProps {
  visible: boolean;
  onClose: () => void;
}

const PluginManager: React.FC<PluginManagerProps> = ({ visible, onClose }) => {
  const { t } = useTranslation();
  const [plugins, setPlugins] = useState<PanelPlugin[]>([]);
  const [filteredPlugins, setFilteredPlugins] = useState<PanelPlugin[]>([]);
  const [activeTab, setActiveTab] = useState('installed');
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedPlugin, setSelectedPlugin] = useState<PanelPlugin | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);

  const pluginManager = PanelPluginManager.getInstance();

  useEffect(() => {
    if (visible) {
      loadPlugins();
    }
  }, [visible]);

  useEffect(() => {
    filterPlugins();
  }, [plugins, searchText, categoryFilter, activeTab]);

  const loadPlugins = () => {
    const allPlugins = pluginManager.getAllPlugins();
    setPlugins(allPlugins);
  };

  const filterPlugins = () => {
    let filtered = plugins;

    // 根据标签页过滤
    if (activeTab === 'active') {
      filtered = filtered.filter(plugin => plugin.config.enabled);
    } else if (activeTab === 'inactive') {
      filtered = filtered.filter(plugin => !plugin.config.enabled);
    }

    // 根据搜索文本过滤
    if (searchText) {
      filtered = filtered.filter(plugin =>
        plugin.manifest.name.toLowerCase().includes(searchText.toLowerCase()) ||
        plugin.manifest.description.toLowerCase().includes(searchText.toLowerCase()) ||
        plugin.manifest.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // 根据分类过滤
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(plugin => plugin.manifest.category === categoryFilter);
    }

    setFilteredPlugins(filtered);
  };

  // 切换插件状态
  const handleTogglePlugin = (pluginId: string, enabled: boolean) => {
    if (enabled) {
      if (pluginManager.activatePlugin(pluginId)) {
        message.success(t('editor.plugins.activated'));
        loadPlugins();
      } else {
        message.error(t('editor.plugins.activationFailed'));
      }
    } else {
      if (pluginManager.deactivatePlugin(pluginId)) {
        message.success(t('editor.plugins.deactivated'));
        loadPlugins();
      } else {
        message.error(t('editor.plugins.deactivationFailed'));
      }
    }
  };

  // 卸载插件
  const handleUninstallPlugin = (pluginId: string) => {
    if (pluginManager.unregisterPlugin(pluginId)) {
      message.success(t('editor.plugins.uninstalled'));
      loadPlugins();
    } else {
      message.error(t('editor.plugins.uninstallFailed'));
    }
  };

  // 查看插件详情
  const handleViewDetails = (plugin: PanelPlugin) => {
    setSelectedPlugin(plugin);
    setDetailVisible(true);
  };

  // 安装插件
  const handleInstallPlugin = (file: File) => {
    // 这里实现插件安装逻辑
    message.info(t('editor.plugins.installationNotImplemented'));
    return false;
  };

  // 获取所有分类
  const getCategories = () => {
    const categories = new Set(plugins.map(plugin => plugin.manifest.category));
    return Array.from(categories);
  };

  // 获取插件状态图标
  const getStatusIcon = (plugin: PanelPlugin) => {
    if (plugin.config.enabled) {
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    } else {
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  // 渲染插件卡片
  const renderPluginCard = (plugin: PanelPlugin) => (
    <List.Item key={plugin.manifest.id}>
      <Card
        size="small"
        title={
          <Space>
            <Avatar icon={plugin.manifest.icon || <AppstoreOutlined />} size="small" />
            <span>{plugin.manifest.name}</span>
            <Badge status={plugin.config.enabled ? 'success' : 'default'} />
          </Space>
        }
        extra={
          <Space>
            <Switch
              checked={plugin.config.enabled}
              onChange={(checked) => handleTogglePlugin(plugin.manifest.id, checked)}
              size="small"
            />
            <Tooltip title={t('editor.plugins.viewDetails')}>
              <Button
                type="text"
                icon={<InfoCircleOutlined />}
                size="small"
                onClick={() => handleViewDetails(plugin)}
              />
            </Tooltip>
            <Popconfirm
              title={t('editor.plugins.confirmUninstall')}
              onConfirm={() => handleUninstallPlugin(plugin.manifest.id)}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Popconfirm>
          </Space>
        }
        actions={[
          <Button
            type="link"
            size="small"
            onClick={() => handleViewDetails(plugin)}
          >
            {t('editor.plugins.details')}
          </Button>
        ]}
      >
        <div className="plugin-card-content">
          <p className="plugin-description">{plugin.manifest.description}</p>
          <div className="plugin-meta">
            <Space wrap>
              <Tag color="blue">{plugin.manifest.category}</Tag>
              <Tag>{plugin.manifest.version}</Tag>
              {plugin.manifest.tags.map(tag => (
                <Tag key={tag} color="default">{tag}</Tag>
              ))}
            </Space>
          </div>
        </div>
      </Card>
    </List.Item>
  );

  return (
    <>
      <Modal
        title={t('editor.plugins.pluginManager')}
        open={visible}
        onCancel={onClose}
        width={900}
        footer={null}
        className="plugin-manager"
      >
        <div className="plugin-manager-content">
          {/* 工具栏 */}
          <div className="plugin-toolbar">
            <Space>
              <Search
                placeholder={t('editor.plugins.searchPlugins')}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 200 }}
              />
              <Select
                value={categoryFilter}
                onChange={setCategoryFilter}
                style={{ width: 120 }}
              >
                <Select.Option value="all">{t('editor.plugins.allCategories')}</Select.Option>
                {getCategories().map(category => (
                  <Select.Option key={category} value={category}>
                    {category}
                  </Select.Option>
                ))}
              </Select>
            </Space>
            
            <Space>
              <Upload
                accept=".zip,.js,.jsx,.ts,.tsx"
                beforeUpload={handleInstallPlugin}
                showUploadList={false}
              >
                <Button icon={<UploadOutlined />}>
                  {t('editor.plugins.installPlugin')}
                </Button>
              </Upload>
            </Space>
          </div>

          {/* 标签页 */}
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'installed',
                label: `${t('editor.plugins.installed')} (${plugins.length})`,
                children: (
                  <List
                    grid={{ gutter: 16, column: 1 }}
                    dataSource={filteredPlugins}
                    renderItem={renderPluginCard}
                    locale={{ emptyText: t('editor.plugins.noPluginsFound') }}
                  />
                )
              },
              {
                key: 'active',
                label: `${t('editor.plugins.active')} (${plugins.filter(p => p.config.enabled).length})`,
                children: (
                  <List
                    grid={{ gutter: 16, column: 1 }}
                    dataSource={filteredPlugins}
                    renderItem={renderPluginCard}
                    locale={{ emptyText: t('editor.plugins.noActivePlugins') }}
                  />
                )
              },
              {
                key: 'inactive',
                label: `${t('editor.plugins.inactive')} (${plugins.filter(p => !p.config.enabled).length})`,
                children: (
                  <List
                    grid={{ gutter: 16, column: 1 }}
                    dataSource={filteredPlugins}
                    renderItem={renderPluginCard}
                    locale={{ emptyText: t('editor.plugins.noInactivePlugins') }}
                  />
                )
              }
            ]}
          />
        </div>
      </Modal>

      {/* 插件详情对话框 */}
      <Modal
        title={selectedPlugin?.manifest.name}
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        width={600}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
      >
        {selectedPlugin && (
          <div className="plugin-details">
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label={t('editor.plugins.name')}>
                {selectedPlugin.manifest.name}
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.plugins.version')}>
                {selectedPlugin.manifest.version}
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.plugins.author')}>
                {selectedPlugin.manifest.author}
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.plugins.category')}>
                {selectedPlugin.manifest.category}
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.plugins.description')}>
                {selectedPlugin.manifest.description}
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.plugins.status')}>
                <Space>
                  {getStatusIcon(selectedPlugin)}
                  {selectedPlugin.config.enabled ? t('editor.plugins.active') : t('editor.plugins.inactive')}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label={t('editor.plugins.tags')}>
                <Space wrap>
                  {selectedPlugin.manifest.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </Space>
              </Descriptions.Item>
              {selectedPlugin.manifest.dependencies && selectedPlugin.manifest.dependencies.length > 0 && (
                <Descriptions.Item label={t('editor.plugins.dependencies')}>
                  <Space wrap>
                    {selectedPlugin.manifest.dependencies.map(dep => (
                      <Tag key={dep} color="orange">{dep}</Tag>
                    ))}
                  </Space>
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
        )}
      </Modal>
    </>
  );
};

export default PluginManager;
