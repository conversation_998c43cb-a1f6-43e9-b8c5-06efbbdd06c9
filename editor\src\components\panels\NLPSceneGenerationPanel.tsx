/**
 * 自然语言场景生成面板
 * 提供基于自然语言描述生成3D场景的功能
 */
import React, { useState, useCallback } from 'react';
import {
  Card, Input, Button, Select, Slider, Space, Typography,
  Progress, Alert, Tooltip, Row, Col,
  Modal, message
} from 'antd';
import {
  SendOutlined, HistoryOutlined, SettingOutlined,
  EyeOutlined, SaveOutlined, DeleteOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useEngineService } from '../../hooks/useEngineService';
import ScenePreview from '../nlp/ScenePreview';
import GenerationHistory from '../nlp/GenerationHistory';
import './NLPSceneGenerationPanel.less';

const { TextArea } = Input;
const { Text } = Typography;

interface GenerationOptions {
  style: string;
  quality: number;
  maxObjects: number;
  constraints?: {
    maxPolygons?: number;
    targetFrameRate?: number;
  };
}

interface GenerationRecord {
  id: string;
  text: string;
  style: string;
  quality: number;
  maxObjects: number;
  timestamp: Date;
  scene?: any;
  understanding?: any;
  metadata?: {
    generationTime: number;
    objectCount: number;
    polygonCount: number;
  };
}

const NLPSceneGenerationPanel: React.FC = () => {
  const { t } = useTranslation();
  const engineService = useEngineService();

  // 状态管理
  const [inputText, setInputText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generatedScene, setGeneratedScene] = useState<any>(null);
  const [generationHistory, setGenerationHistory] = useState<GenerationRecord[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // 生成参数
  const [options, setOptions] = useState<GenerationOptions>({
    style: 'realistic',
    quality: 80,
    maxObjects: 50,
    constraints: {
      maxPolygons: 80000,
      targetFrameRate: 60
    }
  });

  // 预设模板
  const templates = [
    { label: '现代办公室', value: '创建一个现代化的办公室，包含玻璃桌子、舒适的椅子和绿色植物' },
    { label: '温馨客厅', value: '设计一个温馨的客厅，有沙发、茶几、电视和装饰画' },
    { label: '图书馆', value: '建造一个安静的图书馆，有书架、阅读桌和柔和的灯光' },
    { label: '咖啡厅', value: '创建一个舒适的咖啡厅，有吧台、座椅和温暖的氛围' },
    { label: '科幻实验室', value: '设计一个未来感的科幻实验室，有高科技设备和蓝色光效' }
  ];

  // 风格选项
  const styleOptions = [
    { label: '写实风格', value: 'realistic', icon: '🏢' },
    { label: '卡通风格', value: 'cartoon', icon: '🎨' },
    { label: '简约风格', value: 'minimalist', icon: '⚪' },
    { label: '科幻风格', value: 'scifi', icon: '🚀' },
    { label: '奇幻风格', value: 'fantasy', icon: '🏰' }
  ];

  // 生成场景
  const handleGenerateScene = useCallback(async () => {
    if (!inputText.trim()) {
      message.warning('请输入场景描述');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);
    setGeneratedScene(null);

    try {
      const engine = engineService.getEngine();
      const nlpGenerator = engine?.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      const generationOptions = {
        style: options.style,
        quality: options.quality,
        maxObjects: options.maxObjects,
        constraints: options.constraints,
        onProgress: (progress: number) => {
          setGenerationProgress(progress);
        }
      };

      // 修复：使用类型断言来调用底层引擎方法
      const scene = await (nlpGenerator as any).generateSceneFromNaturalLanguage?.(
        inputText,
        generationOptions
      ) || await (nlpGenerator as any).generateScene?.(
        inputText,
        generationOptions
      ) || await (nlpGenerator as any).generate?.(
        inputText,
        generationOptions
      );

      setGeneratedScene(scene);

      // 添加到历史记录
      const historyItem: GenerationRecord = {
        id: Date.now().toString(),
        text: inputText,
        style: options.style,
        quality: options.quality,
        maxObjects: options.maxObjects,
        timestamp: new Date(),
        scene,
        metadata: {
          generationTime: Date.now() - Date.now(),
          objectCount: scene.entities?.length || 0,
          polygonCount: (scene.entities?.length || 0) * 1000
        }
      };

      setGenerationHistory(prev => [historyItem, ...prev.slice(0, 9)]);
      message.success('场景生成成功！');

    } catch (error) {
      console.error('场景生成失败:', error);
      message.error(`场景生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  }, [inputText, options, engineService]);

  // 预览场景
  const handlePreviewScene = useCallback(() => {
    if (generatedScene) {
      setShowPreview(true);
    }
  }, [generatedScene]);

  // 保存场景
  const handleSaveScene = useCallback(async () => {
    if (!generatedScene) return;

    try {
      // 这里应该调用场景保存服务
      message.success('场景保存成功！');
    } catch (error) {
      message.error('场景保存失败');
    }
  }, [generatedScene]);

  // 使用模板
  const handleUseTemplate = useCallback((template: string) => {
    setInputText(template);
  }, []);

  // 清空历史
  const handleClearHistory = useCallback(() => {
    Modal.confirm({
      title: '确认清空历史记录？',
      content: '此操作不可撤销',
      onOk: () => {
        setGenerationHistory([]);
        message.success('历史记录已清空');
      }
    });
  }, []);

  return (
    <div className="nlp-scene-generation-panel">
      <Card 
        title={
          <Space>
            <RobotOutlined />
            <span>自然语言场景生成</span>
          </Space>
        }
        size="small"
        extra={
          <Space>
            <Tooltip title="设置">
              <Button
                size="small"
                icon={<SettingOutlined />}
                onClick={() => setShowSettings(!showSettings)}
              />
            </Tooltip>
            <Tooltip title="历史记录">
              <Button
                size="small"
                icon={<HistoryOutlined />}
                onClick={() => setShowHistory(!showHistory)}
              />
            </Tooltip>
          </Space>
        }
      >
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          {/* 快速模板 */}
          <Card size="small" title="快速模板" type="inner">
            <Space wrap>
              {templates.map((template, index) => (
                <Button
                  key={index}
                  size="small"
                  onClick={() => handleUseTemplate(template.value)}
                  disabled={isGenerating}
                >
                  {template.label}
                </Button>
              ))}
            </Space>
          </Card>

          {/* 文本输入区域 */}
          <div>
            <Text strong>场景描述</Text>
            <TextArea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="请描述您想要创建的场景，例如：创建一个现代化的办公室，包含玻璃桌子、舒适的椅子和绿色植物..."
              rows={4}
              maxLength={500}
              showCount
              disabled={isGenerating}
            />
          </div>

          {/* 生成参数配置 */}
          {showSettings && (
            <Card size="small" title="生成参数" type="inner">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text>风格:</Text>
                  <Select
                    value={options.style}
                    onChange={(value) => setOptions(prev => ({ ...prev, style: value }))}
                    style={{ width: '100%', marginTop: 4 }}
                    disabled={isGenerating}
                  >
                    {styleOptions.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        <Space>
                          <span>{option.icon}</span>
                          <span>{option.label}</span>
                        </Space>
                      </Select.Option>
                    ))}
                  </Select>
                </div>

                <Row gutter={16}>
                  <Col span={12}>
                    <Text>质量等级: {options.quality}</Text>
                    <Slider
                      value={options.quality}
                      onChange={(value) => setOptions(prev => ({ ...prev, quality: value }))}
                      min={20}
                      max={100}
                      step={10}
                      disabled={isGenerating}
                      marks={{
                        20: '低',
                        50: '中',
                        80: '高',
                        100: '极高'
                      }}
                    />
                  </Col>
                  <Col span={12}>
                    <Text>最大对象数: {options.maxObjects}</Text>
                    <Slider
                      value={options.maxObjects}
                      onChange={(value) => setOptions(prev => ({ ...prev, maxObjects: value }))}
                      min={10}
                      max={100}
                      step={5}
                      disabled={isGenerating}
                      marks={{
                        10: '10',
                        50: '50',
                        100: '100'
                      }}
                    />
                  </Col>
                </Row>
              </Space>
            </Card>
          )}

          {/* 操作按钮 */}
          <Space>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleGenerateScene}
              loading={isGenerating}
              disabled={!inputText.trim()}
            >
              {isGenerating ? '生成中...' : '生成场景'}
            </Button>

            <Button
              icon={<EyeOutlined />}
              onClick={handlePreviewScene}
              disabled={!generatedScene}
            >
              预览
            </Button>

            <Button
              icon={<SaveOutlined />}
              onClick={handleSaveScene}
              disabled={!generatedScene}
            >
              保存
            </Button>
          </Space>

          {/* 生成进度 */}
          {isGenerating && (
            <Card size="small" type="inner">
              <Progress
                percent={generationProgress}
                status="active"
                format={(percent) => `生成中... ${percent}%`}
              />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                正在分析语言并生成3D场景...
              </Text>
            </Card>
          )}

          {/* 生成结果提示 */}
          {generatedScene && !isGenerating && (
            <Alert
              message="场景生成完成"
              description="您可以预览、保存或继续编辑生成的场景"
              type="success"
              showIcon
              closable
            />
          )}
        </Space>
      </Card>

      {/* 历史记录面板 */}
      {showHistory && (
        <Card
          title="生成历史"
          size="small"
          style={{ marginTop: 16 }}
          extra={
            <Button
              size="small"
              icon={<DeleteOutlined />}
              onClick={handleClearHistory}
              disabled={generationHistory.length === 0}
            >
              清空
            </Button>
          }
        >
          <GenerationHistory
            history={generationHistory}
            onPreview={(item) => {
              setGeneratedScene(item.scene);
              setShowPreview(true);
            }}
            onRegenerate={(item) => {
              setInputText(item.text);
              setOptions({
                style: item.style,
                quality: item.quality,
                maxObjects: item.maxObjects
              });
            }}
            onDelete={(id) => {
              setGenerationHistory(prev => prev.filter(item => item.id !== id));
            }}
          />
        </Card>
      )}

      {/* 场景预览模态框 */}
      <Modal
        title="场景预览"
        open={showPreview}
        onCancel={() => setShowPreview(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setShowPreview(false)}>
            关闭
          </Button>,
          <Button key="save" type="primary" onClick={handleSaveScene}>
            保存场景
          </Button>
        ]}
      >
        <ScenePreview scene={generatedScene} />
      </Modal>
    </div>
  );
};

export default NLPSceneGenerationPanel;
