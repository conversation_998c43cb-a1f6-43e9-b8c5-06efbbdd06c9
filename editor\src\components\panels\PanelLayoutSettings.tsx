/**
 * 面板布局设置组件
 */
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Tabs,
  List,
  Card,
  Button,
  Input,
  Switch,
  Select,
  Space,
  Popconfirm,
  message,
  Upload,
  Divider,
  Row,
  Col,
  Typography
} from 'antd';
import {
  SaveOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  SettingOutlined,
  PictureOutlined,
  StarOutlined,
  StarFilled
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { LayoutData } from 'rc-dock';
import PanelLayoutManager, { LayoutPreset, UserPreferences } from '../../services/PanelLayoutManager';
import './PanelLayoutSettings.less';

const { TabPane } = Tabs;
const { Text, Title } = Typography;
const { TextArea } = Input;

interface PanelLayoutSettingsProps {
  visible: boolean;
  onClose: () => void;
  currentLayout?: LayoutData;
  onLayoutLoad?: (layout: LayoutData) => void;
}

const PanelLayoutSettings: React.FC<PanelLayoutSettingsProps> = ({
  visible,
  onClose,
  currentLayout,
  onLayoutLoad
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('presets');
  const [layouts, setLayouts] = useState<LayoutPreset[]>([]);
  const [preferences, setPreferences] = useState<UserPreferences>();
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [saveForm, setSaveForm] = useState({ name: '', description: '' });

  const layoutManager = PanelLayoutManager.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
    }
  }, [visible]);

  const loadData = () => {
    setLayouts(layoutManager.getAllLayouts());
    setPreferences(layoutManager.getPreferences());
  };

  // 保存当前布局
  const handleSaveLayout = () => {
    if (!currentLayout) {
      message.error(t('editor.panels.noCurrentLayout'));
      return;
    }
    setSaveModalVisible(true);
  };

  const confirmSaveLayout = () => {
    if (!saveForm.name.trim()) {
      message.error(t('editor.panels.layoutNameRequired'));
      return;
    }

    const id = `layout_${Date.now()}`;
    layoutManager.saveLayout(id, saveForm.name, currentLayout!, saveForm.description);
    setSaveModalVisible(false);
    setSaveForm({ name: '', description: '' });
    loadData();
    message.success(t('editor.panels.layoutSaved'));
  };

  // 加载布局
  const handleLoadLayout = (preset: LayoutPreset) => {
    onLayoutLoad?.(preset.layout);
    message.success(t('editor.panels.layoutLoaded', { name: preset.name }));
  };

  // 删除布局
  const handleDeleteLayout = (id: string) => {
    layoutManager.deleteLayout(id);
    loadData();
    message.success(t('editor.panels.layoutDeleted'));
  };

  // 设置默认布局
  const handleSetDefault = (id: string) => {
    layoutManager.setDefaultLayout(id);
    loadData();
    message.success(t('editor.panels.defaultLayoutSet'));
  };

  // 更新偏好设置
  const handlePreferenceChange = (key: keyof UserPreferences, value: any) => {
    const newPreferences = { ...preferences!, [key]: value };
    setPreferences(newPreferences);
    layoutManager.updatePreferences({ [key]: value });
  };

  // 导出布局
  const handleExportLayouts = () => {
    const data = layoutManager.exportLayouts();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dl-editor-layouts-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    message.success(t('editor.panels.layoutsExported'));
  };

  // 导入布局
  const handleImportLayouts = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target?.result as string;
        if (layoutManager.importLayouts(data)) {
          loadData();
          message.success(t('editor.panels.layoutsImported'));
        } else {
          message.error(t('editor.panels.importFailed'));
        }
      } catch (error) {
        message.error(t('editor.panels.importFailed'));
      }
    };
    reader.readAsText(file);
    return false; // 阻止默认上传行为
  };

  // 重置所有设置
  const handleReset = () => {
    layoutManager.reset();
    loadData();
    message.success(t('editor.panels.settingsReset'));
  };

  return (
    <Modal
      title={t('editor.panels.layoutSettings')}
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
      className="panel-layout-settings"
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('editor.panels.layoutPresets')} key="presets">
          <div className="presets-tab">
            <div className="presets-header">
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveLayout}
                disabled={!currentLayout}
              >
                {t('editor.panels.saveCurrentLayout')}
              </Button>
              <Space>
                <Button icon={<DownloadOutlined />} onClick={handleExportLayouts}>
                  {t('editor.panels.export')}
                </Button>
                <Upload
                  accept=".json"
                  beforeUpload={handleImportLayouts}
                  showUploadList={false}
                >
                  <Button icon={<UploadOutlined />}>
                    {t('editor.panels.import')}
                  </Button>
                </Upload>
              </Space>
            </div>

            <List
              grid={{ gutter: 16, column: 2 }}
              dataSource={layouts}
              renderItem={(preset) => (
                <List.Item>
                  <Card
                    size="small"
                    title={
                      <Space>
                        {preset.name}
                        {preset.isDefault && <StarFilled style={{ color: '#faad14' }} />}
                      </Space>
                    }
                    extra={
                      <Space>
                        <Button
                          type="text"
                          icon={preset.isDefault ? <StarFilled /> : <StarOutlined />}
                          onClick={() => handleSetDefault(preset.id)}
                          title={t('editor.panels.setAsDefault') || '设为默认'}
                        />
                        <Popconfirm
                          title={t('editor.panels.confirmDelete') || '确认删除'}
                          onConfirm={() => handleDeleteLayout(preset.id)}
                        >
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            title={t('editor.panels.delete') || '删除'}
                          />
                        </Popconfirm>
                      </Space>
                    }
                    actions={[
                      <Button
                        type="link"
                        onClick={() => handleLoadLayout(preset)}
                      >
                        {t('editor.panels.load')}
                      </Button>
                    ]}
                  >
                    <Text type="secondary">{preset.description}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {t('editor.panels.lastModified')}: {new Date(preset.updatedAt).toLocaleString()}
                    </Text>
                  </Card>
                </List.Item>
              )}
            />
          </div>
        </TabPane>

        <TabPane tab={t('editor.panels.preferences')} key="preferences">
          <div className="preferences-tab">
            {preferences && (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Card title={t('editor.panels.generalSettings')} size="small">
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Text>{t('editor.panels.autoSave')}</Text>
                        <Switch
                          checked={preferences.autoSave}
                          onChange={(checked) => handlePreferenceChange('autoSave', checked)}
                        />
                      </Space>
                    </Col>
                    <Col span={12}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Text>{t('editor.panels.autoRestore')}</Text>
                        <Switch
                          checked={preferences.autoRestore}
                          onChange={(checked) => handlePreferenceChange('autoRestore', checked)}
                        />
                      </Space>
                    </Col>
                  </Row>
                </Card>

                <Card title={t('editor.panels.appearance')} size="small">
                  <Row gutter={[16, 16]}>
                    <Col span={8}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Text>{t('editor.panels.theme')}</Text>
                        <Select
                          value={preferences.theme}
                          onChange={(value) => handlePreferenceChange('theme', value)}
                          style={{ width: '100%' }}
                        >
                          <Select.Option value="light">{t('editor.panels.lightTheme')}</Select.Option>
                          <Select.Option value="dark">{t('editor.panels.darkTheme')}</Select.Option>
                          <Select.Option value="compact">{t('editor.panels.compactTheme')}</Select.Option>
                        </Select>
                      </Space>
                    </Col>
                    <Col span={8}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Text>{t('editor.panels.compactMode')}</Text>
                        <Switch
                          checked={preferences.compactMode}
                          onChange={(checked) => handlePreferenceChange('compactMode', checked)}
                        />
                      </Space>
                    </Col>
                    <Col span={8}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Text>{t('editor.panels.showToolbar')}</Text>
                        <Switch
                          checked={preferences.showToolbar}
                          onChange={(checked) => handlePreferenceChange('showToolbar', checked)}
                        />
                      </Space>
                    </Col>
                  </Row>
                </Card>

                <Divider />
                <Popconfirm
                  title={t('editor.panels.confirmReset')}
                  onConfirm={handleReset}
                >
                  <Button danger>
                    {t('editor.panels.resetAllSettings')}
                  </Button>
                </Popconfirm>
              </Space>
            )}
          </div>
        </TabPane>
      </Tabs>

      {/* 保存布局对话框 */}
      <Modal
        title={t('editor.panels.saveLayout')}
        open={saveModalVisible}
        onOk={confirmSaveLayout}
        onCancel={() => setSaveModalVisible(false)}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>{t('editor.panels.layoutName')}</Text>
            <Input
              value={saveForm.name}
              onChange={(e) => setSaveForm({ ...saveForm, name: e.target.value })}
              placeholder={t('editor.panels.enterLayoutName') || '请输入布局名称'}
            />
          </div>
          <div>
            <Text>{t('editor.panels.description')}</Text>
            <TextArea
              value={saveForm.description}
              onChange={(e) => setSaveForm({ ...saveForm, description: e.target.value })}
              placeholder={t('editor.panels.enterDescription') || '请输入描述'}
              rows={3}
            />
          </div>
        </Space>
      </Modal>
    </Modal>
  );
};

export default PanelLayoutSettings;
